/**
 * GDPR Database Service
 * 
 * Handles all database operations for GDPR compliance scans.
 * Follows the same pattern as HIPAA database service for consistency.
 */

import db from '@lib/db';
import { env } from '@lib/env';
import { 
  GdprScanEntity, 
  GdprCheckResultEntity, 
  GdprCheckResult,
  RiskLevel,
  ScanStatus 
} from '../types';

export interface GdprScanConfig {
  userId: string;
  targetUrl: string;
  scanOptions: Record<string, unknown>;
}

export interface GdprScanUpdateData {
  overallScore: number;
  riskLevel: RiskLevel;
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  manualReviewRequired: number;
  scanDuration: number;
  status: ScanStatus;
}

export class GdprDatabase {
  /**
   * Test database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      console.log('🔌 Testing GDPR database connection...');
      console.log('📋 Connection string (masked):', env.DATABASE_URL?.replace(/:[^:@]*@/, ':***@'));

      // Add timeout to prevent hanging
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Database connection timeout after 10 seconds')), 10000);
      });

      const testPromise = db.raw('SELECT 1 as test');
      
      await Promise.race([testPromise, timeoutPromise]);
      
      console.log('✅ GDPR database connection successful');
      return true;
    } catch (error) {
      console.error('❌ GDPR database connection failed:', error);
      return false;
    }
  }

  /**
   * Check if GDPR tables exist
   */
  async checkTableExists(): Promise<boolean> {
    try {
      const tableExists = await db.schema.hasTable('gdpr_scans');
      console.log(`📋 GDPR tables exist: ${tableExists}`);
      return tableExists;
    } catch (error) {
      console.error('❌ Error checking GDPR table existence:', error);
      return false;
    }
  }

  /**
   * Create a new GDPR scan record
   */
  async createScan(config: GdprScanConfig): Promise<string> {
    try {
      console.log('📝 Creating GDPR scan record...');
      
      const [scan] = await db('gdpr_scans')
        .insert({
          user_id: config.userId,
          target_url: config.targetUrl,
          scan_timestamp: new Date(),
          total_checks: 0,
          passed_checks: 0,
          failed_checks: 0,
          manual_review_required: 0,
          scan_status: 'pending',
          metadata: config.scanOptions
        })
        .returning('id');

      const scanId = typeof scan === 'object' ? scan.id : scan;
      console.log(`✅ GDPR scan record created with ID: ${scanId}`);
      return scanId;
    } catch (error) {
      console.error('❌ Error creating GDPR scan record:', error);
      throw error;
    }
  }

  /**
   * Update scan status
   */
  async updateScanStatus(
    scanId: string, 
    status: ScanStatus, 
    errorMessage?: string
  ): Promise<void> {
    try {
      console.log(`📝 Updating GDPR scan ${scanId} status to: ${status}`);
      
      const updateData: Partial<GdprScanEntity> = {
        scan_status: status,
        updated_at: new Date()
      };

      if (errorMessage) {
        updateData.error_message = errorMessage;
      }

      await db('gdpr_scans')
        .where('id', scanId)
        .update(updateData);

      console.log(`✅ GDPR scan status updated successfully`);
    } catch (error) {
      console.error('❌ Error updating GDPR scan status:', error);
      throw error;
    }
  }

  /**
   * Update scan with final results
   */
  async updateScanResults(scanId: string, data: GdprScanUpdateData): Promise<void> {
    try {
      console.log(`📝 Updating GDPR scan ${scanId} with final results...`);
      
      await db('gdpr_scans')
        .where('id', scanId)
        .update({
          scan_duration: data.scanDuration,
          overall_score: data.overallScore,
          risk_level: data.riskLevel,
          total_checks: data.totalChecks,
          passed_checks: data.passedChecks,
          failed_checks: data.failedChecks,
          manual_review_required: data.manualReviewRequired,
          scan_status: data.status,
          updated_at: new Date()
        });

      console.log(`✅ GDPR scan results updated successfully`);
    } catch (error) {
      console.error('❌ Error updating GDPR scan results:', error);
      throw error;
    }
  }

  /**
   * Store individual check results
   */
  async storeCheckResults(scanId: string, checkResults: GdprCheckResult[]): Promise<void> {
    try {
      console.log(`📝 Storing ${checkResults.length} GDPR check results...`);
      
      const checkEntities = checkResults.map(result => ({
        scan_id: scanId,
        rule_id: result.ruleId,
        rule_name: result.ruleName,
        category: result.category,
        passed: result.passed,
        score: result.score,
        weight: result.weight,
        severity: result.severity,
        manual_review_required: result.manualReviewRequired,
        evidence: result.evidence,
        recommendations: result.recommendations,
        created_at: new Date()
      }));

      await db('gdpr_check_results').insert(checkEntities);
      
      console.log(`✅ GDPR check results stored successfully`);
    } catch (error) {
      console.error('❌ Error storing GDPR check results:', error);
      throw error;
    }
  }

  /**
   * Get scan by ID
   */
  async getScanById(scanId: string): Promise<GdprScanEntity | null> {
    try {
      const scan = await db('gdpr_scans')
        .where('id', scanId)
        .first();

      return scan || null;
    } catch (error) {
      console.error('❌ Error fetching GDPR scan:', error);
      throw error;
    }
  }

  /**
   * Get check results for a scan
   */
  async getCheckResults(scanId: string): Promise<GdprCheckResultEntity[]> {
    try {
      const results = await db('gdpr_check_results')
        .where('scan_id', scanId)
        .orderBy('created_at', 'asc');

      return results;
    } catch (error) {
      console.error('❌ Error fetching GDPR check results:', error);
      throw error;
    }
  }

  /**
   * Get scans for a user
   */
  async getUserScans(userId: string, limit = 10): Promise<GdprScanEntity[]> {
    try {
      const scans = await db('gdpr_scans')
        .where('user_id', userId)
        .orderBy('scan_timestamp', 'desc')
        .limit(limit);

      return scans;
    } catch (error) {
      console.error('❌ Error fetching user GDPR scans:', error);
      throw error;
    }
  }
}

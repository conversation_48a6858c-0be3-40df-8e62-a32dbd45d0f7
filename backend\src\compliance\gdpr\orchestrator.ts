/**
 * GDPR Compliance Orchestrator
 * 
 * This class coordinates all GDPR compliance checks and manages the scanning process.
 * It follows the same pattern as the HIPAA orchestrator for consistency.
 */

import { 
  GdprScanRequest, 
  GdprScanResult, 
  GdprCheckResult,
  GdprScanSummary,
  RiskLevel,
  ScanStatus 
} from './types';
import { GDPR_RULES, CATEGORY_WEIGHTS, RISK_THRESHOLDS } from './constants';
import { GdprDatabase } from './database/gdpr-database';

export class GdprOrchestrator {
  private database: GdprDatabase;

  constructor() {
    this.database = new GdprDatabase();
  }

  /**
   * Execute a comprehensive GDPR compliance scan
   */
  async executeScan(
    scanRequest: GdprScanRequest,
    userId: string
  ): Promise<GdprScanResult> {
    const startTime = Date.now();
    console.log(`🚀 Starting GDPR compliance scan for: ${scanRequest.targetUrl}`);

    let scanId: string;
    try {
      // Test database connection
      console.log('🔌 Testing database connection...');
      const dbConnected = await this.database.testConnection();
      if (!dbConnected) {
        throw new Error('Database connection failed');
      }

      // Create scan record
      console.log('📝 Creating scan record...');
      scanId = await this.database.createScan({
        userId,
        targetUrl: scanRequest.targetUrl,
        scanOptions: scanRequest.scanOptions || {}
      });

      // Update scan status to running
      await this.database.updateScanStatus(scanId, 'running');

      // Execute all GDPR checks
      console.log('🔍 Executing GDPR compliance checks...');
      const checkResults = await this.executeAllChecks(scanRequest, scanId);

      // Calculate overall score and risk level
      const { overallScore, riskLevel } = this.calculateOverallScore(checkResults);

      // Generate summary
      const summary = this.generateSummary(checkResults);

      // Generate recommendations
      const recommendations = this.generateRecommendations(checkResults);

      // Calculate scan duration
      const scanDuration = Date.now() - startTime;

      // Update scan with results
      await this.database.updateScanResults(scanId, {
        overallScore,
        riskLevel,
        totalChecks: checkResults.length,
        passedChecks: checkResults.filter(c => c.passed).length,
        failedChecks: checkResults.filter(c => !c.passed).length,
        manualReviewRequired: checkResults.filter(c => c.manualReviewRequired).length,
        scanDuration,
        status: 'completed'
      });

      // Store individual check results
      await this.database.storeCheckResults(scanId, checkResults);

      const result: GdprScanResult = {
        scanId,
        targetUrl: scanRequest.targetUrl,
        timestamp: new Date().toISOString(),
        scanDuration,
        overallScore,
        riskLevel,
        status: 'completed',
        summary,
        checks: checkResults,
        recommendations,
        metadata: {
          version: '1.0.0',
          processingTime: scanDuration,
          checksPerformed: checkResults.length,
          analysisLevelsUsed: ['automated', 'pattern-matching'],
          errors: [],
          warnings: [],
          userAgent: scanRequest.scanOptions?.userAgent || 'GDPR-Scanner/1.0',
          scanOptions: scanRequest.scanOptions
        }
      };

      console.log(`🎉 GDPR compliance scan completed successfully!`);
      console.log(`📊 Final Results:`, {
        scanId,
        overallScore,
        riskLevel,
        passedChecks: summary.passedChecks,
        failedChecks: summary.failedChecks,
        scanDuration: `${scanDuration}ms`
      });

      return result;

    } catch (error) {
      console.error('❌ GDPR compliance scan failed:', error);

      // Update scan status if we have a scan ID
      if (scanId!) {
        try {
          await this.database.updateScanStatus(
            scanId,
            'failed',
            error instanceof Error ? error.message : 'Unknown error'
          );
        } catch (dbError) {
          console.warn('⚠️ Failed to update scan status in database:', dbError);
        }
      }

      throw error;
    }
  }

  /**
   * Execute all GDPR compliance checks
   */
  private async executeAllChecks(
    scanRequest: GdprScanRequest,
    scanId: string
  ): Promise<GdprCheckResult[]> {
    const results: GdprCheckResult[] = [];

    // For now, we'll create placeholder results for all rules
    // In later parts, we'll implement actual check logic
    for (const rule of Object.values(GDPR_RULES)) {
      const checkResult: GdprCheckResult = {
        ruleId: rule.id,
        ruleName: rule.name,
        category: rule.category,
        passed: false, // Will be determined by actual checks
        score: 0,
        weight: rule.weight,
        severity: rule.severity,
        evidence: [],
        recommendations: [],
        manualReviewRequired: !rule.automated
      };

      results.push(checkResult);
    }

    return results;
  }

  /**
   * Calculate overall score and risk level
   */
  private calculateOverallScore(checkResults: GdprCheckResult[]): {
    overallScore: number;
    riskLevel: RiskLevel;
  } {
    let totalWeightedScore = 0;
    let totalWeight = 0;

    for (const result of checkResults) {
      const score = result.passed ? 100 : 0;
      totalWeightedScore += score * result.weight;
      totalWeight += result.weight;
    }

    const overallScore = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;

    // Determine risk level based on score
    let riskLevel: RiskLevel;
    if (overallScore < RISK_THRESHOLDS.CRITICAL) {
      riskLevel = 'critical';
    } else if (overallScore < RISK_THRESHOLDS.HIGH) {
      riskLevel = 'high';
    } else if (overallScore < RISK_THRESHOLDS.MEDIUM) {
      riskLevel = 'medium';
    } else {
      riskLevel = 'low';
    }

    return { overallScore: Math.round(overallScore * 100) / 100, riskLevel };
  }

  /**
   * Generate scan summary
   */
  private generateSummary(checkResults: GdprCheckResult[]): GdprScanSummary {
    const totalChecks = checkResults.length;
    const passedChecks = checkResults.filter(c => c.passed).length;
    const failedChecks = checkResults.filter(c => !c.passed).length;
    const manualReviewRequired = checkResults.filter(c => c.manualReviewRequired).length;
    const criticalFailures = checkResults.filter(c => !c.passed && c.severity === 'critical').length;

    // Generate category breakdown
    const categoryBreakdown = Object.keys(CATEGORY_WEIGHTS).map(category => {
      const categoryChecks = checkResults.filter(c => c.category === category);
      const categoryPassed = categoryChecks.filter(c => c.passed).length;
      const categoryScore = categoryChecks.length > 0 ? (categoryPassed / categoryChecks.length) * 100 : 0;

      return {
        category: category as any,
        score: Math.round(categoryScore * 100) / 100,
        checksInCategory: categoryChecks.length,
        passedInCategory: categoryPassed
      };
    });

    return {
      totalChecks,
      passedChecks,
      failedChecks,
      manualReviewRequired,
      criticalFailures,
      categoryBreakdown
    };
  }

  /**
   * Generate recommendations based on failed checks
   */
  private generateRecommendations(checkResults: GdprCheckResult[]): any[] {
    // For now, return empty array
    // Will be implemented in later parts
    return [];
  }
}
